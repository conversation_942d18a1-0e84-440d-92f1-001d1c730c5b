import React, { useState } from "react";
import { Box, Typography, Paper, List, ListItem, ListItemText } from "@mui/material";
import { useTranslation } from "react-i18next";

const AdminPage: React.FC = () => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState("1");
  const [errorsList, setErrorsList] = useState<ListErrorProps[]>([]);
  const [approvalsList, setApprovalsList] = useState<ApprovalProps[]>([]);

  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
  };

  const handleErrorsListChange = (errors: ListErrorProps[]) => {
    setErrorsList(errors);
  };

  const handleApprovalListChange = (approvals: ApprovalProps[]) => {
    setApprovalsList(approvals);
  };

  const renderContent = () => {
    switch (currentTab) {
      case "1":
        return (
          <Typography variant="h6" gutterBottom>
            {t("Pendencies")}
          </Typography>
        );
      case "2":
        return (
          <Typography variant="h6" gutterBottom>
            {t("Permissions")}
          </Typography>
        );
      case "3":
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {t("Error.Log")}
            </Typography>
            {errorsList.length > 0 ? (
              <List>
                {errorsList.map((error) => (
                  <ListItem key={error.id} divider>
                    <ListItemText
                      primary={`${error.method} ${error.requestPath}`}
                      secondary={
                        <>
                          <Typography variant="body2">
                            Status: {error.statusCode} | Process: {error.process}
                          </Typography>
                          <Typography variant="body2">
                            Message: {error.message}
                          </Typography>
                          <Typography variant="body2">
                            Date: {new Date(error.created).toLocaleString()}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="textSecondary">
                {t("No errors found")}
              </Typography>
            )}
          </Box>
        );
      case "4":
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {t("Approvals")}
            </Typography>
            {approvalsList.length > 0 ? (
              <List>
                {approvalsList.map((approval) => (
                  <ListItem key={approval.id} divider>
                    <ListItemText
                      primary={`${approval.process} - ${approval.document}`}
                      secondary={
                        <>
                          <Typography variant="body2">
                            Action: {approval.action} | Origin: {approval.origin}
                          </Typography>
                          <Typography variant="body2">
                            Date: {new Date(approval.created).toLocaleString()}
                          </Typography>
                          <Typography variant="body2">
                            IP: {approval.ip}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="textSecondary">
                {t("No approvals found")}
              </Typography>
            )}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ marginTop: "2rem" }}>
      <Typography variant="h4" gutterBottom>
        Admin
      </Typography>
      
     
      <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
        {renderContent()}
      </Paper>
    </Box>
  );
};

export default AdminPage;
